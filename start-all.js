#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');

const apps = [
    {
        name: 'Subscription',
        dir: 'Saas-Crm-Subscription-Frontend',
        port: 3000,
        color: '\x1b[30m'
    },
    {
        name: 'Auth',
        dir: 'Saas-Crm-Auth-Frontend',
        port: 3001,
        color: '\x1b[31m'
    },
    {
        name: 'Dashboard',
        dir: 'Saas-Crm-Dashboard-Frontend',
        port: 3002,
        color: '\x1b[32m'
    },
    {
        name: 'Inventory',
        dir: 'Saas-Crm-Inventory-Frontend',
        port: 3003,
        color: '\x1b[33m'
    },
    {
        name: 'Lead/Sales',
        dir: 'Saas-Crm-Lead-Frontend',
        port: 3004,
        color: '\x1b[34m'
    },
    {
        name: 'User',
        dir: 'Saas-Crm-User-Frontend',
        port: 3005,
        color: '\x1b[35m'
    },
    {
        name: 'Ticket',
        dir: 'Saas-Crm-Ticket-Frontend',
        port: 3006,
        color: '\x1b[36m'
    },
    {
        name: 'Marketing',
        dir: 'Saas-Crm-Marketing-Frontend',
        port: 3007,
        color: '\x1b[37m'
    },
    {
        name: 'Tenant',
        dir: 'Saas-Crm-Tenant-Frontend',
        port: 3008,
        color: '\x1b[38m'
    }
];

const reset = '\x1b[0m';

console.log('🚀 Starting all CRM applications...\n');

function startApp(app) {
    const appPath = path.join(__dirname, app.dir);

    console.log(`${app.color}[${app.name}]${reset} Starting on port ${app.port}...`);

    const child = spawn('npm', ['run', 'dev'], {
        cwd: appPath,
        stdio: 'pipe',
        shell: true
    });

    child.stdout.on('data', (data) => {
        const output = data.toString().trim();
        if (output) {
            console.log(`${app.color}[${app.name}]${reset} ${output}`);
        }
    });

    child.stderr.on('data', (data) => {
        const output = data.toString().trim();
        if (output) {
            console.log(`${app.color}[${app.name}]${reset} ${output}`);
        }
    });

    child.on('close', (code) => {
        console.log(`${app.color}[${app.name}]${reset} Process exited with code ${code}`);
    });

    return child;
}

const processes = apps.map(startApp);

process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down all applications...');
    processes.forEach((child, index) => {
        console.log(`Stopping ${apps[index].name}...`);
        child.kill('SIGINT');
    });
    process.exit(0);
});

console.log('\n📋 Applications starting:');
apps.forEach(app => {
    console.log(`  • ${app.name}: http://localhost:${app.port}/${app.dir.toLowerCase().replace('saas-crm-', '').replace('-frontend', '')}`);
});

console.log('\n💡 Press Ctrl+C to stop all applications');
console.log('🌐 Main navigation available from any application via the sidebar menu\n');
