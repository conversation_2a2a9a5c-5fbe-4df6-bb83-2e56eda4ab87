import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
    Building2, 
    Users, 
    Settings, 
    LogOut, 
    LayoutDashboard,
    Menu,
    X
} from 'lucide-react';

import { Button } from '../components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import { Alert, AlertDescription } from '../components/ui/alert';
import { getTenantStorageKey, buildSubdomainUrl } from '../utils/subdomain';
import apiClient from '../lib/apiClient';

const TenantDashboard = ({ subdomain }) => {
    const navigate = useNavigate();
    const [user, setUser] = useState(null);
    const [tenantInfo, setTenantInfo] = useState(null);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState('');
    const [sidebarOpen, setSidebarOpen] = useState(false);

    useEffect(() => {
        const loadUserData = () => {
            try {
                // Get tenant-specific user data
                const userKey = getTenantStorageKey('user');
                const tokenKey = getTenantStorageKey('token');
                
                const userData = localStorage.getItem(userKey);
                const token = localStorage.getItem(tokenKey);
                
                if (!userData || !token) {
                    // Redirect to login if no auth data
                    const loginUrl = buildSubdomainUrl(subdomain, '/signin');
                    window.location.href = loginUrl;
                    return;
                }
                
                setUser(JSON.parse(userData));
            } catch (error) {
                console.error('Error loading user data:', error);
                setError('Failed to load user data');
            }
        };

        const loadTenantInfo = async () => {
            try {
                const response = await apiClient.get(`/tenants/${subdomain}`);
                if (response.data.success) {
                    setTenantInfo(response.data.tenant);
                }
            } catch (error) {
                console.error('Error loading tenant info:', error);
                setError('Failed to load tenant information');
            } finally {
                setIsLoading(false);
            }
        };

        loadUserData();
        loadTenantInfo();
    }, [subdomain]);

    const handleLogout = () => {
        // Clear tenant-specific storage
        const keys = ['token', 'user', 'tenantId', 'userId', 'name', 'role', 'permissions'];
        keys.forEach(key => {
            localStorage.removeItem(getTenantStorageKey(key));
        });
        localStorage.removeItem('current_tenant');
        
        // Redirect to tenant login
        const loginUrl = buildSubdomainUrl(subdomain, '/signin');
        window.location.href = loginUrl;
    };

    const navigateToModule = (module, port) => {
        const moduleUrl = `https://${module}.tclaccord.com:${port}`;
        
        // Pass authentication data via URL parameters
        const token = localStorage.getItem(getTenantStorageKey('token'));
        const tenantId = localStorage.getItem(getTenantStorageKey('tenantId'));
        const userId = localStorage.getItem(getTenantStorageKey('userId'));
        const name = localStorage.getItem(getTenantStorageKey('name'));
        const role = localStorage.getItem(getTenantStorageKey('role'));
        
        const params = new URLSearchParams({
            token,
            tenantId,
            userId,
            name,
            role,
            subdomain
        });
        
        window.location.href = `${moduleUrl}?${params.toString()}`;
    };

    const menuItems = [
        {
            name: 'Dashboard',
            icon: LayoutDashboard,
            action: () => navigate('/dashboard'),
            active: true
        },
        {
            name: 'Users',
            icon: Users,
            action: () => navigateToModule('user', 3002),
        },
        {
            name: 'Tickets',
            icon: Building2,
            action: () => navigateToModule('ticket', 3009),
        },
        {
            name: 'Leads',
            icon: Users,
            action: () => navigateToModule('lead', 3004),
        },
        {
            name: 'Inventory',
            icon: Building2,
            action: () => navigateToModule('inventory', 3003),
        },
        {
            name: 'Marketing',
            icon: Building2,
            action: () => navigateToModule('marketing', 3005),
        }
    ];

    if (isLoading) {
        return (
            <div className="min-h-screen flex items-center justify-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gray-50">
            {/* Mobile sidebar backdrop */}
            {sidebarOpen && (
                <div 
                    className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
                    onClick={() => setSidebarOpen(false)}
                />
            )}

            {/* Sidebar */}
            <div className={`fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0 ${
                sidebarOpen ? 'translate-x-0' : '-translate-x-full'
            }`}>
                <div className="flex items-center justify-between h-16 px-6 border-b">
                    <div className="flex items-center space-x-2">
                        <Building2 className="h-8 w-8 text-blue-600" />
                        <span className="text-xl font-bold text-gray-900">
                            {tenantInfo?.name || subdomain}
                        </span>
                    </div>
                    <button
                        onClick={() => setSidebarOpen(false)}
                        className="lg:hidden"
                    >
                        <X className="h-6 w-6" />
                    </button>
                </div>

                <nav className="mt-6">
                    {menuItems.map((item) => (
                        <button
                            key={item.name}
                            onClick={item.action}
                            className={`w-full flex items-center px-6 py-3 text-left hover:bg-gray-50 transition-colors ${
                                item.active ? 'bg-blue-50 text-blue-600 border-r-2 border-blue-600' : 'text-gray-700'
                            }`}
                        >
                            <item.icon className="h-5 w-5 mr-3" />
                            {item.name}
                        </button>
                    ))}
                </nav>

                <div className="absolute bottom-0 w-full p-6 border-t">
                    <div className="flex items-center space-x-3 mb-4">
                        <div className="h-10 w-10 bg-blue-600 rounded-full flex items-center justify-center">
                            <span className="text-white font-medium">
                                {user?.name?.charAt(0)?.toUpperCase()}
                            </span>
                        </div>
                        <div>
                            <p className="text-sm font-medium text-gray-900">{user?.name}</p>
                            <p className="text-xs text-gray-500">{user?.role}</p>
                        </div>
                    </div>
                    <Button
                        onClick={handleLogout}
                        variant="outline"
                        className="w-full"
                    >
                        <LogOut className="h-4 w-4 mr-2" />
                        Logout
                    </Button>
                </div>
            </div>

            {/* Main content */}
            <div className="lg:ml-64">
                {/* Header */}
                <header className="bg-white shadow-sm border-b">
                    <div className="flex items-center justify-between h-16 px-6">
                        <div className="flex items-center space-x-4">
                            <button
                                onClick={() => setSidebarOpen(true)}
                                className="lg:hidden"
                            >
                                <Menu className="h-6 w-6" />
                            </button>
                            <h1 className="text-2xl font-bold text-gray-900">
                                Dashboard
                            </h1>
                        </div>
                        <div className="text-sm text-gray-500">
                            {subdomain}.tclaccord.com
                        </div>
                    </div>
                </header>

                {/* Content */}
                <main className="p-6">
                    {error && (
                        <Alert variant="destructive" className="mb-6">
                            <AlertDescription>{error}</AlertDescription>
                        </Alert>
                    )}

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                        <Card>
                            <CardHeader>
                                <CardTitle className="text-lg">Welcome Back!</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <p className="text-gray-600">
                                    Welcome to your {subdomain} dashboard. 
                                    You're logged in as <strong>{user?.name}</strong>.
                                </p>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader>
                                <CardTitle className="text-lg">Tenant Info</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-2">
                                    <p><strong>Name:</strong> {tenantInfo?.name || subdomain}</p>
                                    <p><strong>Domain:</strong> {subdomain}.tclaccord.com</p>
                                    <p><strong>Status:</strong> Active</p>
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader>
                                <CardTitle className="text-lg">Quick Actions</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-2">
                                    <Button 
                                        onClick={() => navigateToModule('user', 3002)}
                                        className="w-full"
                                        variant="outline"
                                    >
                                        Manage Users
                                    </Button>
                                    <Button 
                                        onClick={() => navigateToModule('ticket', 3009)}
                                        className="w-full"
                                        variant="outline"
                                    >
                                        View Tickets
                                    </Button>
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    <Card>
                        <CardHeader>
                            <CardTitle>Available Modules</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                {menuItems.slice(1).map((item) => (
                                    <Button
                                        key={item.name}
                                        onClick={item.action}
                                        variant="outline"
                                        className="h-20 flex flex-col items-center justify-center space-y-2"
                                    >
                                        <item.icon className="h-6 w-6" />
                                        <span>{item.name}</span>
                                    </Button>
                                ))}
                            </div>
                        </CardContent>
                    </Card>
                </main>
            </div>
        </div>
    );
};

export default TenantDashboard;
