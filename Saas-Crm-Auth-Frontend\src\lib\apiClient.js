import axios from 'axios';
import { getApiUrl } from '../config/api';
import { getSubdomain, getTenantStorageKey } from '../utils/subdomain';

const apiClient = axios.create({
    baseURL: getApiUrl(),
    timeout: 30000, // 30 seconds
    headers: {
        'Content-Type': 'application/json',
    },
});

console.log('API Base URL:', getApiUrl());
console.log('Current Subdomain:', getSubdomain());

// Request interceptor to add tenant-specific headers and authentication
apiClient.interceptors.request.use((config) => {
    const subdomain = getSubdomain();

    // Add subdomain header for tenant identification
    if (subdomain) {
        config.headers['X-Tenant-Subdomain'] = subdomain;

        // Use tenant-specific token
        const tokenKey = getTenantStorageKey('token');
        const token = localStorage.getItem(tokenKey);

        if (token) {
            const cleanToken = token.trim().replace(/[^\x00-\x7F]/g, "");
            config.headers.Authorization = `Bearer ${cleanToken}`;
        }
    } else {
        // Main domain - use regular token
        const token = localStorage.getItem('token');
        if (token) {
            const cleanToken = token.trim().replace(/[^\x00-\x7F]/g, "");
            config.headers.Authorization = `Bearer ${cleanToken}`;
        }
    }

    return config;
}, (error) => Promise.reject(error));

// Response interceptor to handle tenant-specific errors
apiClient.interceptors.response.use(
    (response) => response,
    (error) => {
        const subdomain = getSubdomain();

        // Handle 401 errors (unauthorized) - but not for login requests
        if (error.response?.status === 401 && !error.config?.url?.includes('/auth/login')) {
            // Clear tenant-specific or main domain storage
            if (subdomain) {
                const keys = ['token', 'user', 'tenantId', 'userId', 'name', 'role', 'permissions'];
                keys.forEach(key => {
                    localStorage.removeItem(getTenantStorageKey(key));
                });
                localStorage.removeItem('current_tenant');

                // Redirect to tenant login
                window.location.href = `https://${subdomain}.tclaccord.com/signin`;
            } else {
                // Clear main domain storage
                localStorage.clear();
                window.location.href = '/signin';
            }
        }

        // Handle 403 errors (forbidden) - tenant not found or invalid
        if (error.response?.status === 403 && subdomain) {
            console.error('Tenant access forbidden:', subdomain);
            // Could redirect to main domain or show error page
        }

        return Promise.reject(error);
    }
);

export default apiClient;