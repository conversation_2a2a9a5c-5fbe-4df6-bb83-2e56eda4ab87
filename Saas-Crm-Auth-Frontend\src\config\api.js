/**
 * API Configuration for Multi-Tenant Architecture
 * Handles API URL generation and tenant-specific configurations
 */

import { getSubdomain, getTenantStorageKey } from '../utils/subdomain';

/**
 * Get the appropriate API URL based on current subdomain
 * @returns {string} - API base URL
 */
export const getApiUrl = () => {
    const subdomain = getSubdomain();
    
    // For development
    if (import.meta.env.DEV) {
        return (
            import.meta.env.VITE_API_URL ||
            import.meta.env.VITE_API_BASE_URL ||
            import.meta.env.VITE_API_BASE_URL_AUTH ||
            'http://localhost:8000/api'
        );
    }
    
    // For production - same API for all tenants
    return (
        import.meta.env.VITE_API_URL ||
        import.meta.env.VITE_API_BASE_URL ||
        import.meta.env.VITE_API_BASE_URL_AUTH ||
        'https://api.tclaccord.com'
    );
};

/**
 * Get tenant-specific API headers
 * @returns {Object} - Headers object
 */
export const getTenantHeaders = () => {
    const subdomain = getSubdomain();
    const headers = {
        'Content-Type': 'application/json',
    };
    
    // Add subdomain header for tenant identification
    if (subdomain) {
        headers['X-Tenant-Subdomain'] = subdomain;
    }
    
    // Add authentication token if available
    const tokenKey = getTenantStorageKey('token');
    const token = localStorage.getItem(tokenKey);
    
    if (token) {
        headers['Authorization'] = `Bearer ${token}`;
    }
    
    return headers;
};

/**
 * Get tenant-specific configuration
 * @returns {Object} - Configuration object
 */
export const getTenantConfig = () => {
    const subdomain = getSubdomain();
    
    return {
        subdomain,
        apiUrl: getApiUrl(),
        headers: getTenantHeaders(),
        isMainDomain: !subdomain,
        isTenant: !!subdomain
    };
};

/**
 * Build API endpoint URL
 * @param {string} endpoint - API endpoint path
 * @returns {string} - Complete API URL
 */
export const buildApiUrl = (endpoint) => {
    const baseUrl = getApiUrl();
    const cleanEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
    return `${baseUrl}${cleanEndpoint}`;
};

/**
 * Environment-specific configurations
 */
export const API_CONFIG = {
    // Main API URL
    BASE_URL: getApiUrl(),
    
    // Timeout settings
    TIMEOUT: 30000, // 30 seconds
    
    // Retry settings
    RETRY_ATTEMPTS: 3,
    RETRY_DELAY: 1000, // 1 second
    
    // Endpoints
    ENDPOINTS: {
        // Authentication
        LOGIN: '/auth/login',
        TENANT_LOGIN: '/auth/tenant-login',
        LOGOUT: '/auth/logout',
        REFRESH: '/auth/refresh',
        
        // Tenant management
        TENANTS: '/tenants',
        VALIDATE_TENANT: '/tenants/validate',
        
        // User management
        USERS: '/users',
        PROFILE: '/users/profile',
        
        // Other modules
        TICKETS: '/tickets',
        LEADS: '/leads',
        INVENTORY: '/inventory',
        MARKETING: '/marketing'
    }
};

/**
 * Module port mappings for cross-module navigation
 */
export const MODULE_PORTS = {
    auth: 3001,
    user: 3002,
    inventory: 3003,
    lead: 3004,
    marketing: 3005,
    subscription: 3006,
    tenant: 3007,
    dashboard: 3008,
    ticket: 3009,
    license: 3010
};

/**
 * Build module URL for cross-module navigation
 * @param {string} module - Module name
 * @param {string} path - Path within module (optional)
 * @param {Object} params - URL parameters (optional)
 * @returns {string} - Complete module URL
 */
export const buildModuleUrl = (module, path = '', params = {}) => {
    const port = MODULE_PORTS[module];
    if (!port) {
        throw new Error(`Unknown module: ${module}`);
    }
    
    const baseUrl = `https://${module}.tclaccord.com:${port}`;
    const cleanPath = path.startsWith('/') ? path : `/${path}`;
    
    // Add authentication and tenant parameters
    const subdomain = getSubdomain();
    if (subdomain) {
        const tokenKey = getTenantStorageKey('token');
        const token = localStorage.getItem(tokenKey);
        
        if (token) {
            params.token = token;
            params.subdomain = subdomain;
            params.tenantId = localStorage.getItem(getTenantStorageKey('tenantId'));
            params.userId = localStorage.getItem(getTenantStorageKey('userId'));
            params.role = localStorage.getItem(getTenantStorageKey('role'));
        }
    }
    
    const queryString = Object.keys(params).length > 0 
        ? `?${new URLSearchParams(params).toString()}`
        : '';
    
    return `${baseUrl}${cleanPath}${queryString}`;
};

/**
 * Default export with all configurations
 */
export default {
    getApiUrl,
    getTenantHeaders,
    getTenantConfig,
    buildApiUrl,
    buildModuleUrl,
    API_CONFIG,
    MODULE_PORTS
};
